const BaseTask = require('./BaseTask');

/**
 * 每分钟执行的任务
 */
class MinuteTask extends BaseTask {
    constructor() {
        super('MinuteTask');
    }

    /**
     * 启动任务
     */
    async startup() {
        console.log(`[Worker ${this.processId}] 启动每分钟任务...`);
        
        // 每分钟执行一次
        this.createScheduleJob('* * * * *', async () => {
            await this.execute();
        });
    }

    /**
     * 执行任务逻辑
     */
    async execute() {
        // 模拟一些业务逻辑
        console.log(`[Worker ${this.processId}] 执行每分钟任务 - 检查系统状态`);
        
        // 这里可以添加具体的业务逻辑
        // 例如：检查数据库连接、清理临时文件、发送心跳等
        
        // 模拟异步操作
        await new Promise(resolve => setTimeout(resolve, 100));
        
        console.log(`[Worker ${this.processId}] 每分钟任务执行完成 - 系统状态正常`);
    }
}

module.exports = MinuteTask;
