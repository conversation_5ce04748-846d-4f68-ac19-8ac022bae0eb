const TaskManager = require('./TaskManager');

/**
 * 定时任务系统统一接口
 * 对外暴露简洁的 API，隐藏内部实现细节
 */
class ScheduleSystem {
    constructor() {
        this.taskManager = new TaskManager();
        this.processId = process.pid;
        this.isInitialized = false;
    }

    /**
     * 初始化并启动定时任务系统
     */
    async initialize() {
        if (this.isInitialized) {
            console.log(`[Worker ${this.processId}] 定时任务系统已经初始化，跳过重复初始化`);
            return;
        }

        try {
            console.log(`[Worker ${this.processId}] 开始初始化定时任务系统...`);
            
            // 加载所有任务
            await this.taskManager.loadTasks();
            
            // 启动所有任务
            await this.taskManager.startupAll();
            
            this.isInitialized = true;
            console.log(`[Worker ${this.processId}] 定时任务系统初始化完成`);
            
            // 显示任务状态
            this.logTaskStatus();
            
        } catch (error) {
            console.error(`[Worker ${this.processId}] 初始化定时任务系统失败:`, error.message);
            throw error;
        }
    }

    /**
     * 停止定时任务系统
     */
    async shutdown() {
        if (!this.isInitialized) {
            console.log(`[Worker ${this.processId}] 定时任务系统未初始化，无需关闭`);
            return;
        }

        try {
            console.log(`[Worker ${this.processId}] 开始关闭定时任务系统...`);
            
            // 停止所有任务
            await this.taskManager.shutdownAll();
            
            this.isInitialized = false;
            console.log(`[Worker ${this.processId}] 定时任务系统已关闭`);
            
        } catch (error) {
            console.error(`[Worker ${this.processId}] 关闭定时任务系统时发生错误:`, error.message);
            throw error;
        }
    }

    /**
     * 获取任务状态
     */
    getTaskStatus() {
        if (!this.isInitialized) {
            return { status: 'not_initialized', tasks: {} };
        }
        
        return {
            status: 'running',
            taskCount: this.taskManager.getTaskCount(),
            tasks: this.taskManager.getTaskStatus()
        };
    }

    /**
     * 记录任务状态
     */
    logTaskStatus() {
        const status = this.getTaskStatus();
        console.log(`[Worker ${this.processId}] 当前任务状态:`, status.tasks);
    }

    /**
     * 检查系统是否已初始化
     */
    isRunning() {
        return this.isInitialized;
    }

    /**
     * 获取任务数量
     */
    getTaskCount() {
        return this.taskManager.getTaskCount();
    }
}

// 创建单例实例
const scheduleSystem = new ScheduleSystem();

/**
 * 对外暴露的简洁 API
 */
module.exports = {
    /**
     * 初始化定时任务系统
     */
    async init() {
        return await scheduleSystem.initialize();
    },

    /**
     * 停止定时任务系统
     */
    async stop() {
        return await scheduleSystem.shutdown();
    },

    /**
     * 获取任务状态
     */
    getStatus() {
        return scheduleSystem.getTaskStatus();
    },

    /**
     * 检查是否正在运行
     */
    isRunning() {
        return scheduleSystem.isRunning();
    },

    /**
     * 获取任务数量
     */
    getTaskCount() {
        return scheduleSystem.getTaskCount();
    },

    /**
     * 记录任务状态
     */
    logStatus() {
        scheduleSystem.logTaskStatus();
    }
};
