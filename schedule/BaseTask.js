const schedule = require('node-schedule');

/**
 * 基础任务类
 * 所有定时任务都应该继承此类
 */
class BaseTask {
    constructor(taskName = 'UnknownTask') {
        this.taskName = taskName;
        this.job = null;
        this.processId = process.pid;
        this.executeCount = 0;
        this.lastExecuteTime = null;
        this.isTaskRunning = false;
    }

    /**
     * 启动任务 - 子类必须实现
     */
    async startup() {
        throw new Error(`Task ${this.taskName} must implement startup() method`);
    }

    /**
     * 停止任务
     */
    async shutdown() {
        if (this.job) {
            console.log(`[Worker ${this.processId}] 正在停止任务: ${this.taskName}`);
            this.job.cancel();
            this.job = null;
            this.isTaskRunning = false;
            console.log(`[Worker ${this.processId}] 任务 ${this.taskName} 已停止`);
        }
    }

    /**
     * 创建定时任务
     * @param {string} cronExpression - cron 表达式
     * @param {function} taskFunction - 任务执行函数
     */
    createScheduleJob(cronExpression, taskFunction) {
        if (this.job) {
            console.warn(`[Worker ${this.processId}] 任务 ${this.taskName} 已存在，先停止旧任务`);
            this.job.cancel();
        }

        this.job = schedule.scheduleJob(cronExpression, async () => {
            try {
                this.lastExecuteTime = new Date();
                this.executeCount++;
                
                console.log(`[Worker ${this.processId}] 任务 ${this.taskName} 开始执行 (第${this.executeCount}次) - ${this.lastExecuteTime.toLocaleString('zh-CN', {
                    timeZone: 'Asia/Shanghai',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                })}`);

                await taskFunction();
                
                console.log(`[Worker ${this.processId}] 任务 ${this.taskName} 执行完成`);
            } catch (error) {
                console.error(`[Worker ${this.processId}] 任务 ${this.taskName} 执行失败:`, error.message);
            }
        });

        this.isTaskRunning = true;
        console.log(`[Worker ${this.processId}] 任务 ${this.taskName} 已创建，cron: ${cronExpression}`);
    }

    /**
     * 检查任务是否正在运行
     */
    isRunning() {
        return this.isTaskRunning && this.job !== null;
    }

    /**
     * 获取任务信息
     */
    getTaskInfo() {
        return {
            taskName: this.taskName,
            processId: this.processId,
            isRunning: this.isRunning(),
            executeCount: this.executeCount,
            lastExecuteTime: this.lastExecuteTime,
            nextInvocation: this.job ? this.job.nextInvocation() : null
        };
    }

    /**
     * 执行任务逻辑 - 子类应该重写此方法
     */
    async execute() {
        console.log(`[Worker ${this.processId}] 执行基础任务: ${this.taskName}`);
    }
}

module.exports = BaseTask;
