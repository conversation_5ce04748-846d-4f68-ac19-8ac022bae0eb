const fs = require('fs');
const path = require('path');

/**
 * 定时任务管理器
 * 负责加载、启动、停止所有定时任务
 */
class TaskManager {
    constructor() {
        this.tasks = new Map();
        this.processId = process.pid;
        this.isShuttingDown = false;
    }

    /**
     * 加载所有任务模块
     */
    async loadTasks() {
        const scheduleDir = __dirname;
        const files = fs.readdirSync(scheduleDir);
        
        for (const file of files) {
            // 跳过 TaskManager.js、BaseTask.js 和非 .js 文件
            if (file === 'TaskManager.js' || file === 'BaseTask.js' || !file.endsWith('.js')) {
                continue;
            }
            
            try {
                const taskPath = path.join(scheduleDir, file);
                const TaskClass = require(taskPath);
                
                // 检查是否是有效的任务类
                if (typeof TaskClass === 'function' && TaskClass.prototype.startup) {
                    const taskName = path.basename(file, '.js');
                    const taskInstance = new TaskClass();
                    
                    this.tasks.set(taskName, taskInstance);
                    console.log(`[Worker ${this.processId}] 已加载任务: ${taskName}`);
                } else {
                    console.warn(`[Worker ${this.processId}] 跳过无效任务文件: ${file}`);
                }
            } catch (error) {
                console.error(`[Worker ${this.processId}] 加载任务失败 ${file}:`, error.message);
            }
        }
        
        console.log(`[Worker ${this.processId}] 总共加载了 ${this.tasks.size} 个任务`);
    }

    /**
     * 启动所有任务
     */
    async startupAll() {
        if (this.isShuttingDown) {
            console.log(`[Worker ${this.processId}] 正在关闭中，跳过启动任务`);
            return;
        }

        console.log(`[Worker ${this.processId}] 开始启动所有定时任务...`);
        
        for (const [taskName, taskInstance] of this.tasks) {
            try {
                await taskInstance.startup();
                console.log(`[Worker ${this.processId}] 任务 ${taskName} 启动成功`);
            } catch (error) {
                console.error(`[Worker ${this.processId}] 任务 ${taskName} 启动失败:`, error.message);
            }
        }
        
        console.log(`[Worker ${this.processId}] 所有定时任务启动完成`);
    }

    /**
     * 停止所有任务
     */
    async shutdownAll() {
        this.isShuttingDown = true;
        console.log(`[Worker ${this.processId}] 开始停止所有定时任务...`);
        
        const shutdownPromises = [];
        
        for (const [taskName, taskInstance] of this.tasks) {
            const shutdownPromise = (async () => {
                try {
                    await taskInstance.shutdown();
                    console.log(`[Worker ${this.processId}] 任务 ${taskName} 停止成功`);
                } catch (error) {
                    console.error(`[Worker ${this.processId}] 任务 ${taskName} 停止失败:`, error.message);
                }
            })();
            
            shutdownPromises.push(shutdownPromise);
        }
        
        // 等待所有任务停止完成
        await Promise.all(shutdownPromises);
        console.log(`[Worker ${this.processId}] 所有定时任务已停止`);
    }

    /**
     * 获取任务状态
     */
    getTaskStatus() {
        const status = {};
        for (const [taskName, taskInstance] of this.tasks) {
            status[taskName] = {
                isRunning: taskInstance.isRunning ? taskInstance.isRunning() : false,
                lastExecuteTime: taskInstance.lastExecuteTime || null,
                executeCount: taskInstance.executeCount || 0
            };
        }
        return status;
    }

    /**
     * 获取任务数量
     */
    getTaskCount() {
        return this.tasks.size;
    }
}

module.exports = TaskManager;
