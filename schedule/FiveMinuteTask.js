const BaseTask = require('./BaseTask');

/**
 * 每5分钟执行的任务
 */
class FiveMinuteTask extends BaseTask {
    constructor() {
        super('FiveMinuteTask');
    }

    /**
     * 启动任务
     */
    async startup() {
        console.log(`[Worker ${this.processId}] 启动每5分钟任务...`);
        
        // 每5分钟执行一次 (在每小时的第0、5、10、15...分钟执行)
        this.createScheduleJob('*/5 * * * *', async () => {
            await this.execute();
        });
    }

    /**
     * 执行任务逻辑
     */
    async execute() {
        console.log(`[Worker ${this.processId}] 执行每5分钟任务 - 数据同步检查`);
        
        // 模拟数据同步逻辑
        try {
            // 模拟检查数据同步状态
            await this.checkDataSync();
            
            // 模拟清理过期数据
            await this.cleanupExpiredData();
            
            console.log(`[Worker ${this.processId}] 每5分钟任务执行完成 - 数据同步正常`);
        } catch (error) {
            console.error(`[Worker ${this.processId}] 每5分钟任务执行失败:`, error.message);
        }
    }

    /**
     * 检查数据同步状态
     */
    async checkDataSync() {
        console.log(`[Worker ${this.processId}] 检查数据同步状态...`);
        // 模拟异步检查
        await new Promise(resolve => setTimeout(resolve, 200));
        console.log(`[Worker ${this.processId}] 数据同步状态检查完成`);
    }

    /**
     * 清理过期数据
     */
    async cleanupExpiredData() {
        console.log(`[Worker ${this.processId}] 清理过期数据...`);
        // 模拟异步清理
        await new Promise(resolve => setTimeout(resolve, 150));
        console.log(`[Worker ${this.processId}] 过期数据清理完成`);
    }
}

module.exports = FiveMinuteTask;
