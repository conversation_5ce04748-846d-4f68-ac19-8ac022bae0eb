const schedule = require('../schedule');
const http = require('http');

// 获取当前进程 ID
const processId = process.pid;

// HTTP 服务器实例
let httpServer = null;

// 初始化应用
async function initializeApp() {
    try {
        console.log(`[Worker ${processId}] 开始启动应用...`);
        
        // 1. 启动定时任务系统
        await schedule.init();
        console.log(`[Worker ${processId}] 定时任务系统启动完成`);
        
        // 2. 启动 HTTP 服务器
        await startHttpServer();
        console.log(`[Worker ${processId}] HTTP 服务器启动完成`);
        
        // 3. 这里可以添加更多服务
        // await database.connect();
        // await messageQueue.init();
        
        console.log(`[Worker ${processId}] 应用启动完成`);
        
    } catch (error) {
        console.error(`[Worker ${processId}] 应用启动失败:`, error.message);
        process.exit(1);
    }
}

// 启动 HTTP 服务器
async function startHttpServer() {
    return new Promise((resolve, reject) => {
        httpServer = http.createServer((req, res) => {
            if (req.url === '/health') {
                // 健康检查接口
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({
                    status: 'ok',
                    processId: processId,
                    scheduleStatus: schedule.getStatus(),
                    timestamp: new Date().toISOString()
                }));
            } else if (req.url === '/tasks') {
                // 任务状态接口
                res.writeHead(200, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify(schedule.getStatus()));
            } else {
                res.writeHead(404, { 'Content-Type': 'text/plain' });
                res.end('Not Found');
            }
        });

        httpServer.listen(3000 + (processId % 1000), (err) => {
            if (err) {
                reject(err);
            } else {
                console.log(`[Worker ${processId}] HTTP 服务器监听端口: ${httpServer.address().port}`);
                resolve();
            }
        });
    });
}

// 优雅关闭函数
async function gracefulShutdown(signal) {
    console.log(`\n[Worker ${processId}] 收到 ${signal} 信号，正在优雅关闭...`);
    
    try {
        // 1. 停止定时任务系统
        await schedule.stop();
        console.log(`[Worker ${processId}] 定时任务系统已停止`);
        
        // 2. 关闭 HTTP 服务器
        if (httpServer) {
            await new Promise((resolve) => {
                httpServer.close(() => {
                    console.log(`[Worker ${processId}] HTTP 服务器已关闭`);
                    resolve();
                });
            });
        }
        
        // 3. 这里可以添加其他清理逻辑
        // await database.disconnect();
        // await messageQueue.close();
        
        console.log(`[Worker ${processId}] 应用已优雅关闭`);
        process.exit(0);
    } catch (error) {
        console.error(`[Worker ${processId}] 关闭应用时发生错误:`, error.message);
        process.exit(1);
    }
}

// 启动应用
initializeApp();

// 注册信号处理器
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('disconnect', () => gracefulShutdown('DISCONNECT'));

// 错误处理
process.on('uncaughtException', async function(err) {
    console.error(`[Worker ${processId}] 未捕获的异常:`, err);
    await gracefulShutdown('UNCAUGHT_EXCEPTION');
});

process.on('unhandledRejection', async function(reason) {
    console.error(`[Worker ${processId}] 未处理的 Promise 拒绝:`, reason);
    await gracefulShutdown('UNHANDLED_REJECTION');
});
