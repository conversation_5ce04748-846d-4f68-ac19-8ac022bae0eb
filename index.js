const cluster = require('cluster');
const os = require('os');

// 获取 CPU 核心数
const numCPUs = os.cpus().length;

if (cluster.isMaster) {
    console.log(`主进程 ${process.pid} 正在运行`);
    console.log(`CPU 核心数: ${numCPUs}`);

    // 创建 worker 进程数量（可以根据需要调整）
    const workerCount = Math.min(numCPUs, 4); // 最多启动4个worker
    console.log(`准备启动 ${workerCount} 个 worker 进程`);

    // 启动 worker 进程
    for (let i = 0; i < workerCount; i++) {
        const worker = cluster.fork();
        console.log(`Worker ${worker.process.pid} 已启动`);
    }

    // 监听 worker 进程退出事件
    cluster.on('exit', (worker, code, signal) => {
        console.log(`Worker ${worker.process.pid} 已退出，退出码: ${code}, 信号: ${signal}`);

        // 如果 worker 意外退出，重新启动一个新的 worker
        if (code !== 0 && !worker.exitedAfterDisconnect) {
            console.log('重新启动一个新的 worker...');
            const newWorker = cluster.fork();
            console.log(`新的 Worker ${newWorker.process.pid} 已启动`);
        }
    });

    // 监听 worker 进程上线事件
    cluster.on('online', (worker) => {
        console.log(`Worker ${worker.process.pid} 已上线`);
    });

    // 优雅地关闭所有 worker 进程
    process.on('SIGINT', () => {
        console.log('\n收到 SIGINT 信号，正在关闭所有 worker 进程...');

        for (const id in cluster.workers) {
            cluster.workers[id].kill();
        }

        // 等待所有 worker 进程退出
        cluster.on('exit', () => {
            if (Object.keys(cluster.workers).length === 0) {
                console.log('所有 worker 进程已关闭，主进程退出');
                process.exit(0);
            }
        });
    });

    process.on('SIGTERM', () => {
        console.log('\n收到 SIGTERM 信号，正在关闭所有 worker 进程...');

        for (const id in cluster.workers) {
            cluster.workers[id].kill();
        }
    });

} else {
    // Worker 进程
    console.log(`Worker 进程 ${process.pid} 正在启动...`);

    // 加载并运行 app.js
    require('./app.js');

    console.log(`Worker 进程 ${process.pid} 已启动完成`);
}