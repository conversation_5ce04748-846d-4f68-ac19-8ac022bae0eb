hoistPattern:
  - '*'
hoistedDependencies:
  cron-parser@4.9.0:
    cron-parser: private
  long-timeout@0.1.1:
    long-timeout: private
  luxon@3.7.1:
    luxon: private
  sorted-array-functions@1.3.0:
    sorted-array-functions: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.15.0
pendingBuilds: []
prunedAt: Thu, 04 Sep 2025 07:30:18 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
