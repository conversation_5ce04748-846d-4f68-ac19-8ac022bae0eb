lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      node-schedule:
        specifier: ^2.1.1
        version: 2.1.1

packages:

  cron-parser@4.9.0:
    resolution: {integrity: sha512-p0SaNjrHOnQeR8/VnfGbmg9te2kfyYSQ7Sc/j/6DtPL3JQvKxmjO9TSjNFpujqV3vEYYBvNNvXSxzyksBWAx1Q==}
    engines: {node: '>=12.0.0'}

  long-timeout@0.1.1:
    resolution: {integrity: sha512-BFRuQUqc7x2NWxfJBCyUrN8iYUYznzL9JROmRz1gZ6KlOIgmoD+njPVbb+VNn2nGMKggMsK79iUNErillsrx7w==}

  luxon@3.7.1:
    resolution: {integrity: sha512-RkRWjA926cTvz5rAb1BqyWkKbbjzCGchDUIKMCUvNi17j6f6j8uHGDV82Aqcqtzd+icoYpELmG3ksgGiFNNcNg==}
    engines: {node: '>=12'}

  node-schedule@2.1.1:
    resolution: {integrity: sha512-OXdegQq03OmXEjt2hZP33W2YPs/E5BcFQks46+G2gAxs4gHOIVD1u7EqlYLYSKsaIpyKCK9Gbk0ta1/gjRSMRQ==}
    engines: {node: '>=6'}

  sorted-array-functions@1.3.0:
    resolution: {integrity: sha512-2sqgzeFlid6N4Z2fUQ1cvFmTOLRi/sEDzSQ0OKYchqgoPmQBVyM3959qYx3fpS6Esef80KjmpgPeEr028dP3OA==}

snapshots:

  cron-parser@4.9.0:
    dependencies:
      luxon: 3.7.1

  long-timeout@0.1.1: {}

  luxon@3.7.1: {}

  node-schedule@2.1.1:
    dependencies:
      cron-parser: 4.9.0
      long-timeout: 0.1.1
      sorted-array-functions: 1.3.0

  sorted-array-functions@1.3.0: {}
