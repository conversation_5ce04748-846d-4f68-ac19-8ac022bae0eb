{"name": "sorted-array-functions", "version": "1.3.0", "description": "Maintain and search through a sorted array using some low level functions", "main": "index.js", "dependencies": {}, "devDependencies": {"standard": "^8.4.0", "tape": "^4.6.2"}, "scripts": {"test": "standard && tape test.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/sorted-array-functions.git"}, "author": "<PERSON> (@mafintosh)", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/sorted-array-functions/issues"}, "homepage": "https://github.com/mafintosh/sorted-array-functions"}