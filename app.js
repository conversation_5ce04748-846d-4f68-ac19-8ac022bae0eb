const TaskManager = require('./schedule/TaskManager');

// 获取当前进程 ID
const processId = process.pid;

// 创建任务管理器实例
const taskManager = new TaskManager();

// 初始化并启动所有任务
async function initializeTasks() {
    try {
        console.log(`[Worker ${processId}] 开始初始化定时任务系统...`);

        // 加载所有任务
        await taskManager.loadTasks();

        // 启动所有任务
        await taskManager.startupAll();

        console.log(`[Worker ${processId}] 定时任务系统初始化完成`);

        // 显示任务状态
        const taskStatus = taskManager.getTaskStatus();
        console.log(`[Worker ${processId}] 当前任务状态:`, taskStatus);

    } catch (error) {
        console.error(`[Worker ${processId}] 初始化定时任务系统失败:`, error.message);
        process.exit(1);
    }
}

// 启动任务系统
initializeTasks();

// 优雅关闭函数
async function gracefulShutdown(signal) {
    console.log(`\n[Worker ${processId}] 收到 ${signal} 信号，正在优雅关闭...`);

    try {
        // 停止所有定时任务
        await taskManager.shutdownAll();
        console.log(`[Worker ${processId}] 所有定时任务已停止`);
        process.exit(0);
    } catch (error) {
        console.error(`[Worker ${processId}] 关闭定时任务时发生错误:`, error.message);
        process.exit(1);
    }
}

// 优雅地处理程序退出
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// 处理来自主进程的 disconnect 事件
process.on('disconnect', () => gracefulShutdown('DISCONNECT'));

// 错误处理
process.on('uncaughtException', async function(err) {
    console.error(`[Worker ${processId}] 未捕获的异常:`, err);
    try {
        await taskManager.shutdownAll();
    } catch (shutdownError) {
        console.error(`[Worker ${processId}] 关闭任务时发生错误:`, shutdownError.message);
    }
    process.exit(1);
});

process.on('unhandledRejection', async function(reason) {
    console.error(`[Worker ${processId}] 未处理的 Promise 拒绝:`, reason);
    try {
        await taskManager.shutdownAll();
    } catch (shutdownError) {
        console.error(`[Worker ${processId}] 关闭任务时发生错误:`, shutdownError.message);
    }
    process.exit(1);
});
