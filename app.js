const schedule = require('node-schedule');

// 获取当前进程 ID
const processId = process.pid;

// 创建一个每分钟执行一次的定时任务
// cron 表达式 '* * * * *' 表示每分钟执行一次
// 格式：秒 分 时 日 月 星期
const job = schedule.scheduleJob('* * * * *', function() {
    const now = new Date();
    console.log(`[Worker ${processId}] 定时任务执行时间: ${now.toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    })}`);

    // 在这里添加您想要执行的任务逻辑
    console.log(`[Worker ${processId}] 执行定时任务中...`);
});

console.log(`[Worker ${processId}] 定时任务已启动，每分钟执行一次`);

// 优雅地处理程序退出
process.on('SIGINT', function() {
    console.log(`\n[Worker ${processId}] 正在停止定时任务...`);
    job.cancel();
    console.log(`[Worker ${processId}] 定时任务已停止`);
    process.exit(0);
});

// 错误处理
process.on('uncaughtException', function(err) {
    console.error(`[Worker ${processId}] 未捕获的异常:`, err);
    job.cancel();
    process.exit(1);
});

process.on('unhandledRejection', function(reason) {
    console.error(`[Worker ${processId}] 未处理的 Promise 拒绝:`, reason);
    job.cancel();
    process.exit(1);
});
