const schedule = require('./schedule');

// 获取当前进程 ID
const processId = process.pid;

// 初始化定时任务系统
async function initializeApp() {
    try {
        // 启动定时任务系统
        await schedule.init();

        console.log(`[Worker ${processId}] Worker 进程启动完成`);

        // 这里可以添加其他初始化逻辑
        // 例如：启动 HTTP 服务器、连接数据库等

    } catch (error) {
        console.error(`[Worker ${processId}] Worker 进程启动失败:`, error.message);
        process.exit(1);
    }
}

// 启动应用
initializeApp();

// 优雅关闭函数
async function gracefulShutdown(signal) {
    console.log(`\n[Worker ${processId}] 收到 ${signal} 信号，正在优雅关闭...`);

    try {
        // 停止定时任务系统
        await schedule.stop();

        // 这里可以添加其他清理逻辑
        // 例如：关闭数据库连接、停止 HTTP 服务器等

        console.log(`[Worker ${processId}] Worker 进程已优雅关闭`);
        process.exit(0);
    } catch (error) {
        console.error(`[Worker ${processId}] 关闭 Worker 进程时发生错误:`, error.message);
        process.exit(1);
    }
}

// 注册信号处理器
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('disconnect', () => gracefulShutdown('DISCONNECT'));

// 错误处理
process.on('uncaughtException', async function(err) {
    console.error(`[Worker ${processId}] 未捕获的异常:`, err);
    await gracefulShutdown('UNCAUGHT_EXCEPTION');
});

process.on('unhandledRejection', async function(reason) {
    console.error(`[Worker ${processId}] 未处理的 Promise 拒绝:`, reason);
    await gracefulShutdown('UNHANDLED_REJECTION');
});
