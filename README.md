# Node.js 多进程定时任务系统

一个基于 Node.js cluster 模块的高可用定时任务系统，支持多进程并发执行和模块化任务管理。

## 🏗️ 架构设计

### 文件结构
```
├── index.js              # 主进程管理器 (Master Process)
├── app.js                # Worker 进程入口 (简洁的应用启动器)
├── schedule/             # 定时任务目录
│   ├── index.js          # 定时任务系统统一接口
│   ├── TaskManager.js    # 任务管理器 (内部实现)
│   ├── BaseTask.js       # 基础任务类
│   ├── MinuteTask.js     # 每分钟执行的任务示例
│   └── FiveMinuteTask.js # 每5分钟执行的任务示例
├── package.json
└── README.md
```

### 核心组件

#### 1. 主进程管理器 (index.js)
- 管理多个 Worker 进程的生命周期
- 自动重启意外退出的 Worker 进程
- 优雅关闭所有 Worker 进程
- 支持 SIGINT 和 SIGTERM 信号处理

#### 2. Worker 进程 (app.js)
- **简洁的应用启动器**：只负责调用 schedule 模块的接口
- 处理优雅关闭信号
- 为其他功能扩展预留空间 (HTTP 服务器、数据库连接等)

#### 3. 定时任务系统 (schedule/index.js)
- **统一对外接口**：提供 init()、stop() 等简洁 API
- 隐藏内部实现细节
- 单例模式确保系统唯一性

#### 4. 任务管理器 (schedule/TaskManager.js)
- 自动发现和加载 schedule 目录下的任务
- 统一管理任务的启动和停止
- 提供任务状态查询功能

#### 5. 基础任务类 (schedule/BaseTask.js)
- 提供任务的基础生命周期方法
- 封装 node-schedule 的调度逻辑
- 统一的日志和错误处理

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动系统
```bash
node index.js
```

### 停止系统
```bash
# 使用 Ctrl+C 或发送 SIGINT 信号
kill -INT <主进程PID>
```

## 🎯 架构优势

### 简洁的 app.js
重构后的 `app.js` 非常简洁，只有 59 行代码：
- **单一职责**：只负责应用的启动和关闭
- **易于扩展**：为其他功能 (HTTP 服务器、数据库等) 预留空间
- **清晰的接口**：通过 `schedule.init()` 和 `schedule.stop()` 管理定时任务

### 模块化设计
- **schedule/index.js**：对外统一接口，隐藏实现细节
- **schedule/TaskManager.js**：内部任务管理逻辑
- **schedule/BaseTask.js**：任务基类
- **schedule/具体任务.js**：业务任务实现

### 扩展性
当需要添加其他功能时，app.js 不会变得臃肿：
```javascript
// app.js 中可以轻松添加其他服务
async function initializeApp() {
    await schedule.init();        // 定时任务系统
    await database.connect();     // 数据库连接
    await httpServer.start();     // HTTP 服务器
    await messageQueue.init();    // 消息队列
}
```

## 📝 创建新任务

### 1. 创建任务文件
在 `schedule/` 目录下创建新的任务文件，例如 `HourlyTask.js`：

```javascript
const BaseTask = require('./BaseTask');

class HourlyTask extends BaseTask {
    constructor() {
        super('HourlyTask');
    }

    async startup() {
        console.log(`[Worker ${this.processId}] 启动每小时任务...`);
        
        // 每小时执行一次
        this.createScheduleJob('0 * * * *', async () => {
            await this.execute();
        });
    }

    async execute() {
        console.log(`[Worker ${this.processId}] 执行每小时任务`);
        
        // 在这里添加具体的业务逻辑
        // 例如：数据备份、报告生成等
        
        console.log(`[Worker ${this.processId}] 每小时任务执行完成`);
    }
}

module.exports = HourlyTask;
```

### 2. 任务自动加载
任务文件创建后会被 TaskManager 自动发现和加载，无需手动注册。

## 🔧 配置说明

### Worker 进程数量
在 `index.js` 中可以调整 Worker 进程数量：
```javascript
const workerCount = Math.min(numCPUs, 4); // 最多启动4个worker
```

### Cron 表达式
支持标准的 cron 表达式格式：
- `* * * * *` - 每分钟
- `*/5 * * * *` - 每5分钟
- `0 * * * *` - 每小时
- `0 0 * * *` - 每天午夜
- `0 0 * * 0` - 每周日午夜

### 超时设置
在 `index.js` 中可以调整 Worker 进程关闭超时时间：
```javascript
}, 60000); // 60秒超时
```

## 📊 监控和日志

### 日志格式
所有日志都包含 Worker 进程 ID，便于调试：
```
[Worker 12345] 任务 MinuteTask 开始执行 (第1次) - 2025/09/06 08:33:00
```

### 任务状态
系统启动时会显示所有任务的状态：
```javascript
{
  FiveMinuteTask: { isRunning: true, lastExecuteTime: null, executeCount: 0 },
  MinuteTask: { isRunning: true, lastExecuteTime: null, executeCount: 0 }
}
```

## 🛡️ 错误处理

### 任务级别错误
- 单个任务执行失败不会影响其他任务
- 自动记录错误日志
- 任务会在下次调度时间继续执行

### 进程级别错误
- Worker 进程崩溃会自动重启
- 主进程监控所有 Worker 进程状态
- 支持优雅关闭和强制关闭

## 🔄 生命周期

### 启动流程
1. 主进程启动并创建 Worker 进程
2. Worker 进程加载 TaskManager
3. TaskManager 扫描并加载所有任务
4. 调用每个任务的 `startup()` 方法
5. 任务开始按计划执行

### 关闭流程
1. 接收关闭信号 (SIGINT/SIGTERM)
2. 主进程向所有 Worker 发送 disconnect 信号
3. Worker 进程调用所有任务的 `shutdown()` 方法
4. 等待任务优雅停止
5. Worker 进程退出
6. 主进程确认所有 Worker 退出后自己退出

## 🎯 最佳实践

1. **任务设计**：保持任务执行时间短，避免长时间阻塞
2. **错误处理**：在任务中添加适当的 try-catch 处理
3. **资源管理**：及时释放数据库连接等资源
4. **日志记录**：添加详细的执行日志便于调试
5. **监控告警**：可以集成外部监控系统

## 📈 性能特性

- **多进程并发**：充分利用多核 CPU
- **高可用性**：单个进程崩溃不影响整体服务
- **自动恢复**：进程异常退出自动重启
- **优雅关闭**：支持平滑停止所有任务
- **模块化设计**：易于扩展和维护
